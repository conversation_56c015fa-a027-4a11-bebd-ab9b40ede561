# Sơ đồ Luồng <PERSON>ử lý Audience (<PERSON><PERSON><PERSON> tượng khách hàng)

## 1. <PERSON><PERSON> đồ Tổng quan Hệ thống

```mermaid
graph TB
    Client[Client/UI] --> Controller[AudienceController]
    Controller --> Service[AudienceService]
    Service --> QueryBuilder[QueryBuilderES]
    Service --> ProfileRepo[ProfileInfoRepository]
    Service --> AudienceRepo[AudienceRepository]
    Service --> ProfileMgmt[ProfileManagementRepository]
    
    ProfileRepo --> ES[Elasticsearch]
    ProfileRepo --> HBase[HBase]
    AudienceRepo --> MySQL[MySQL Database]
    ProfileMgmt --> MySQL
    
    Service --> KMS[KMS Service]
    
    subgraph "Data Storage"
        ES
        HBase
        MySQL
    end
    
    subgraph "External Services"
        KMS
    end
```

## 2. Luồng Tạo Audience

```mermaid
sequenceDiagram
    participant Client
    participant Controller as AudienceController
    participant Service as AudienceService
    participant Repo as AudienceRepository
    participant DB as MySQL Database
    
    Client->>Controller: POST /audiences
    Controller->>Controller: Hash User ID (CRC32)
    Controller->>Service: createAudience(audienceDto)
    
    Service->>Service: Validate required fields
    Service->>Service: Generate unique ID
    Service->>Service: Set default values
    
    Service->>Repo: save(audienceEntity)
    Repo->>DB: INSERT INTO audiences
    DB-->>Repo: Success
    Repo-->>Service: AudienceEntity
    Service-->>Controller: Audience DTO
    Controller-->>Client: Response with created audience
```

## 3. Luồng Estimate Audience

```mermaid
sequenceDiagram
    participant Client
    participant Controller as AudienceController
    participant Service as AudienceService
    participant ProfileMgmt as ProfileManagementRepo
    participant QueryBuilder as QueryBuilderES
    participant ProfileRepo as ProfileInfoRepository
    participant ES as Elasticsearch
    
    Client->>Controller: POST /audiences/estimate
    Controller->>Controller: Hash User ID
    Controller->>Service: estimate(audienceDto)
    
    Service->>ProfileMgmt: getProfileManagementByUserId()
    ProfileMgmt-->>Service: ProfileManagement with mapping
    
    Service->>Service: Build mapping map
    Service->>Service: Include sub-fields mapping
    
    Service->>QueryBuilder: getSegmentQuery(segmentRule, mappingMap)
    QueryBuilder->>QueryBuilder: Process segment rules
    QueryBuilder->>QueryBuilder: Build BoolQueryBuilder
    QueryBuilder-->>Service: BoolQueryBuilder
    
    Service->>ProfileRepo: estimateSegment(userId, query)
    ProfileRepo->>ES: Count API with query
    ES-->>ProfileRepo: Document count
    ProfileRepo-->>Service: Total profiles
    
    Service-->>Controller: AudienceEstimateDto
    Controller-->>Client: Response with estimate
```

## 4. Luồng Preview Profiles

```mermaid
sequenceDiagram
    participant Client
    participant Controller as AudienceController
    participant Service as AudienceService
    participant ProfileMgmt as ProfileManagementRepo
    participant QueryBuilder as QueryBuilderES
    participant ProfileRepo as ProfileInfoRepository
    participant ES as Elasticsearch
    participant HBase as HBase
    participant KMS as KMS Service
    
    Client->>Controller: POST /audiences/preview-profiles
    Controller->>Service: previewProfiles(audienceDto)
    
    Service->>ProfileMgmt: getProfileManagementByUserId()
    ProfileMgmt-->>Service: ProfileManagement
    
    Service->>Service: Build mapping map
    Service->>QueryBuilder: getSegmentQuery(segmentRule, mappingMap)
    QueryBuilder-->>Service: BoolQueryBuilder
    
    Service->>ProfileRepo: getBySegment(userId, query, page, limit)
    
    ProfileRepo->>ES: Search with pagination
    ES-->>ProfileRepo: Profile IDs
    
    ProfileRepo->>HBase: Batch get profiles by IDs
    HBase-->>ProfileRepo: Raw profile data
    
    ProfileRepo-->>Service: List<ProfileInfo>
    
    Service->>Service: Process profiles in parallel
    Service->>KMS: Decrypt sensitive fields
    KMS-->>Service: Decrypted data
    Service->>Service: Apply masking rules
    
    Service-->>Controller: ProfileDetailDto
    Controller-->>Client: Response with profiles
```

## 5. Luồng Xây dựng Query Elasticsearch

```mermaid
flowchart TD
    Start([Segment Rule Input]) --> CheckNull{Segment Rule null?}
    CheckNull -->|Yes| ReturnNull[Return null]
    CheckNull -->|No| CheckGroups{Single Group?}
    
    CheckGroups -->|Yes| ProcessGroup[Process Single Group]
    CheckGroups -->|No| ProcessMultiple[Process Multiple Groups]
    
    ProcessMultiple --> LoopGroups[Loop through Groups]
    LoopGroups --> BuildGroupQuery[Build Query per Group]
    BuildGroupQuery --> ApplyGroupExpr[Apply Group Expression]
    ApplyGroupExpr --> CombineGroups[Combine Group Queries]
    
    ProcessGroup --> BuildGroupQuery
    BuildGroupQuery --> LoopRules[Loop through Rules]
    LoopRules --> ProcessRule[Process Each Rule]
    
    ProcessRule --> CheckDataType{Data Type?}
    CheckDataType -->|Text| TextQuery[Build Text Query]
    CheckDataType -->|Number| NumberQuery[Build Number Query]
    CheckDataType -->|DateTime| DateQuery[Build Date Query]
    CheckDataType -->|Array| ArrayQuery[Build Array Query]
    CheckDataType -->|Frequency| FreqQuery[Build Frequency Query]
    
    TextQuery --> CheckOperator{Operator?}
    CheckOperator -->|contain| WildcardQuery[WildcardQuery *value*]
    CheckOperator -->|startwith| PrefixQuery[WildcardQuery value*]
    CheckOperator -->|endwith| SuffixQuery[WildcardQuery *value]
    CheckOperator -->|equal| TermQuery[TermQuery exact]
    CheckOperator -->|not_*| NegativeQuery[Add to Negative List]
    
    NumberQuery --> NumOperator{Operator?}
    NumOperator -->|>=,<=,>,<| RangeQuery[RangeQuery with bounds]
    NumOperator -->|equal| NumTermQuery[TermQuery]
    NumOperator -->|between| BetweenQuery[RangeQuery from-to]
    
    DateQuery --> ScriptQuery[ScriptQuery with Painless]
    FreqQuery --> FreqScript[ScriptQuery count pattern]
    ArrayQuery --> ArrayTerm[TermQuery on elements]
    
    WildcardQuery --> AddToPositive[Add to Positive Rules]
    PrefixQuery --> AddToPositive
    SuffixQuery --> AddToPositive
    TermQuery --> AddToPositive
    NumTermQuery --> AddToPositive
    RangeQuery --> AddToPositive
    BetweenQuery --> AddToPositive
    ScriptQuery --> AddToPositive
    FreqScript --> AddToPositive
    ArrayTerm --> AddToPositive
    
    NegativeQuery --> AddToNegative[Add to Negative Rules]
    
    AddToPositive --> ApplyRuleExpr[Apply Rule Expression]
    AddToNegative --> ApplyRuleExpr
    ApplyRuleExpr --> FinalQuery[Final BoolQueryBuilder]
    
    CombineGroups --> FinalQuery
    FinalQuery --> End([Return Query])
    ReturnNull --> End
```

## 6. Luồng Xử lý Dữ liệu Profile

```mermaid
flowchart TD
    Start([Profile IDs from ES]) --> BatchGet[Batch Get from HBase]
    BatchGet --> RawData[Raw Profile Data]
    
    RawData --> ParallelProcess[Parallel Stream Processing]
    ParallelProcess --> DecryptLoop[Loop through Data Fields]
    
    DecryptLoop --> CheckMapping{Mapping exists?}
    CheckMapping -->|No| SkipField[Skip Field]
    CheckMapping -->|Yes| CheckMasking{Check Masking Type}
    
    CheckMasking -->|SYSTEM| SystemMask[Apply System Masking]
    CheckMasking -->|FULL| FullMask[Apply Full Masking]
    CheckMasking -->|PARTIAL| PartialMask[Decrypt & Partial Mask]
    CheckMasking -->|NONE| NoMask[No Masking]
    
    SystemMask --> SetMasked[Set ******** value]
    FullMask --> SetMasked
    PartialMask --> DecryptKMS[Decrypt with KMS]
    NoMask --> DecryptKMS
    
    DecryptKMS --> ApplyMapping[Apply Field Mapping]
    SetMasked --> ApplyMapping
    
    ApplyMapping --> CheckDemographic{Is Fullname?}
    CheckDemographic -->|Yes| SetProfileName[Set Profile Name]
    CheckDemographic -->|No| ContinueLoop[Continue Loop]
    
    SetProfileName --> ContinueLoop
    ContinueLoop --> MoreFields{More Fields?}
    MoreFields -->|Yes| DecryptLoop
    MoreFields -->|No| MoreProfiles{More Profiles?}
    
    MoreProfiles -->|Yes| ParallelProcess
    MoreProfiles -->|No| FormatResponse[Format Response]
    
    SkipField --> ContinueLoop
    FormatResponse --> End([Return Processed Profiles])
```

## 7. Luồng Batch Total Profiles

```mermaid
flowchart TD
    Start([List of Audience IDs]) --> GetAudiences[Get Audiences from DB]
    GetAudiences --> GetProfileMgmt[Get Profile Management]
    GetProfileMgmt --> BuildMapping[Build Mapping Map]
    
    BuildMapping --> ParallelStream[Parallel Stream Processing]
    ParallelStream --> ProcessAudience[Process Each Audience]
    
    ProcessAudience --> UpdateRules[Update Segment Rules with Mapping]
    UpdateRules --> EstimateAsync[Estimate with Timeout]
    
    EstimateAsync --> TimeoutCheck{Timeout 2s?}
    TimeoutCheck -->|Success| UpdateDB[Update Database]
    TimeoutCheck -->|Timeout/Error| FallbackDB[Get from Database Cache]
    
    UpdateDB --> StoreResult[Store in Result Map]
    FallbackDB --> StoreResult
    
    StoreResult --> MoreAudiences{More Audiences?}
    MoreAudiences -->|Yes| ProcessAudience
    MoreAudiences -->|No| ReturnResults[Return Results Map]
    
    ReturnResults --> End([Map<AudienceId, TotalProfiles>])
```

## 8. Sơ đồ Kiến trúc Dữ liệu

```mermaid
erDiagram
    AUDIENCES {
        bigint id PK
        varchar name
        text description
        bigint user_id
        json conditions
        bigint profile_count
        int type
        int is_deleted
        timestamp created_at
        timestamp updated_at
    }
    
    PROFILE_MANAGEMENT {
        bigint id PK
        varchar name
        bigint user_id
        int status
        json mapping
        json sources
        varchar kms_customer_id
        timestamp create_time
        timestamp update_time
    }
    
    ELASTICSEARCH_INDICES {
        string profile_id PK
        json data
        long last_update
        int is_delete
    }
    
    HBASE_TABLES {
        string profile_id PK
        json full_profile_data
        json behaviors
        json insights
    }
    
    AUDIENCES ||--|| PROFILE_MANAGEMENT : "belongs to user"
    PROFILE_MANAGEMENT ||--o{ ELASTICSEARCH_INDICES : "defines schema"
    ELASTICSEARCH_INDICES ||--|| HBASE_TABLES : "references"
```
