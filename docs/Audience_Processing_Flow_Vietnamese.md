# Tài liệu Quy trình Xử lý Audience (Đối tượng khách hàng)

## 1. Tổng quan Kiến trúc

### 1.1 C<PERSON>c thành phần chính
- **AudienceController**: <PERSON><PERSON><PERSON><PERSON> vào API, xử lý các HTTP requests
- **AudienceService**: Logic nghiệp vụ chính cho audience
- **AudienceRepository**: Truy cập dữ liệu MySQL
- **ProfileInfoRepository**: Truy cập dữ liệu Elasticsearch
- **QueryBuilderES**: Xây dựng query Elasticsearch từ segment rules
- **AudienceMapper**: Chuyển đổi giữa Entity và Model

### 1.2 Cấu trúc dữ liệu Audience
```java
public class Audience {
    private String id;                    // ID duy nhất của audience
    private String name;                  // Tên audience
    private String description;           // Mô tả
    private String userId;                // ID người dùng sở hữu
    private Integer profileCount;         // Số lượng profile thỏa mãn
    private Integer isDeleted;            // Trạng thái xóa (0: active, 1: deleted)
    private Integer type;                 // Loại audience (static/dynamic)
    private SegmentRule segmentRule;      // Quy tắc phân đoạn
}
```

### 1.3 Cấu trúc Segment Rule
```java
public static class SegmentRule {
    private String groupExpression;       // Biểu thức logic giữa các groups (AND/OR)
    private List<Group> groups;           // Danh sách các nhóm điều kiện
}

public static class Group {
    private String ruleExpression;        // Biểu thức logic giữa các rules (AND/OR)
    private List<Rule> rules;             // Danh sách các quy tắc
}

public static class Rule {
    private String fieldName;             // Tên trường dữ liệu
    private String dataType;              // Kiểu dữ liệu (string, number, date, etc.)
    private String fieldMapping;          // Mapping field trong Elasticsearch
    private boolean isSystemData;         // Có phải dữ liệu hệ thống không
    private String compare;               // Toán tử so sánh (=, >, <, contains, etc.)
    private Object value;                 // Giá trị so sánh
    private String pattern;               // Pattern cho regex
}
```

## 2. Quy trình Xử lý Chi tiết

### 2.1 Tạo Audience Mới

#### 2.1.1 API Endpoint
```
POST /audiences
Header: user-id
Body: Audience object
```

#### 2.1.2 Các bước xử lý trong AudienceController.createAudience()
1. **Nhận request từ client**
   - Lấy user-id từ header
   - Parse Audience object từ request body

2. **Thiết lập thông tin cơ bản**
   - Set userId từ header vào audienceDto
   - Gọi audienceService.createAudience()

#### 2.1.3 Các bước xử lý trong AudienceService.createAudience()
1. **Validation dữ liệu đầu vào**
   - Kiểm tra name không được null
   - Nếu thiếu name → throw InvalidParamException

2. **Thiết lập metadata**
   - Tạo ID mới bằng sequenceGenerator.nextId()
   - Set isDeleted = 0 (active)
   - Set type = AUDIENCE_TYPE_STATIC

3. **Lưu vào database**
   - Chuyển đổi Audience model → AudienceEntity bằng AudienceMapper
   - Lưu entity vào MySQL thông qua audienceDao.save()

4. **Trả về kết quả**
   - Return audience object đã được tạo

### 2.2 Estimate Audience (Ước tính số lượng)

#### 2.2.1 API Endpoint
```
POST /audiences/estimate
Header: user-id
Body: Audience object với segmentRule
```

#### 2.2.2 Các bước xử lý trong AudienceService.estimate()
1. **Xác định user owner**
   - Nếu audience đã tồn tại (có ID) → lấy userId từ database
   - Nếu audience mới → sử dụng userId từ request

2. **Lấy thông tin Profile Management**
   - Gọi getProfileManagement(userOwner)
   - Kiểm tra tồn tại → nếu không có throw ProfileNotFoundException

3. **Tính tổng số profile**
   - Gọi profileInfoRepository.getTotal(userOwner)
   - Đếm tất cả documents trong Elasticsearch index

4. **Xây dựng mapping map**
   - Tạo mappingMapIncludeSubField từ profileManagement.mapping
   - Bao gồm cả sub-field mappings

5. **Xây dựng và thực thi query**
   - Gọi QueryBuilderES.getSegmentQuery() để tạo BoolQueryBuilder
   - Thực thi query trên Elasticsearch bằng profileInfoRepository.estimateSegment()
   - Đếm số documents thỏa mãn điều kiện

6. **Trả về kết quả**
   - AudienceEstimateDto với totalProfile và satisfiedProfile

### 2.3 Xây dựng Elasticsearch Query

#### 2.3.1 QueryBuilderES.getSegmentQuery()
**Mục đích**: Chuyển đổi SegmentRule thành BoolQueryBuilder của Elasticsearch

**Các bước xử lý:**
1. **Kiểm tra segment rule**
   - Nếu segmentRule == null → return null
   - Nếu chỉ có 1 group → gọi trực tiếp buildQueryPerGroup()

2. **Xử lý multiple groups**
   - Tạo BoolQueryBuilder cho mỗi group
   - Áp dụng groupExpression để kết hợp (AND/OR)

#### 2.3.2 QueryBuilderES.buildQueryPerGroup()
**Mục đích**: Xây dựng query cho một group rules

**Các bước xử lý:**
1. **Khởi tạo query builders**
   - rulesQueryBuilder: Chứa các điều kiện dương (must)
   - negativeRulesQueryBuilder: Chứa các điều kiện âm (must_not)

2. **Xử lý từng rule**
   - Gọi buildSimpleDataTypeQuery() cho mỗi rule
   - Phân loại vào positive hoặc negative queries

3. **Kết hợp queries**
   - Áp dụng ruleExpression để kết hợp các rules (AND/OR)
   - Tạo BoolQueryBuilder cuối cùng

#### 2.3.3 QueryBuilderES.buildSimpleDataTypeQuery()
**Mục đích**: Xây dựng query cho một rule cụ thể

**Xử lý theo data type:**

1. **STRING (Chuỗi)**
   - `equal`: TermQuery
   - `contains`: WildcardQuery với pattern *value*
   - `not_contains`: Thêm vào negativeRulesQueryBuilder
   - `starts_with`: PrefixQuery
   - `ends_with`: WildcardQuery với pattern *value
   - `regex`: RegexpQuery
   - `is_null`: Kiểm tra field không tồn tại
   - `is_not_null`: ExistsQuery

2. **NUMBER (Số)**
   - `=`: TermQuery
   - `>=`, `>`, `<=`, `<`: RangeQuery
   - `!=`: Thêm vào negativeRulesQueryBuilder

3. **DATE/DATETIME (Ngày tháng)**
   - `equal`: ScriptQuery so sánh ngày
   - `before`: ScriptQuery với điều kiện <
   - `after`: ScriptQuery với điều kiện >
   - `between`: RangeQuery với from/to

4. **OBJECT (Đối tượng)**
   - `is_null`: Kiểm tra field không tồn tại
   - `is_not_null`: ExistsQuery

### 2.4 Thực thi Query trên Elasticsearch

#### 2.4.1 ProfileInfoRepository.estimateSegment()
**Mục đích**: Đếm số documents thỏa mãn điều kiện

**Các bước xử lý:**
1. **Tạo CountRequest**
   - Chỉ định index: tableNameElastic + userId
   - Gắn BoolQueryBuilder vào request

2. **Thực thi count query**
   - Gọi elasticsearchReadAdapter.countingDocument()
   - Trả về số lượng documents

3. **Xử lý lỗi**
   - Catch exception và log error
   - Return 0 nếu có lỗi

#### 2.4.2 ProfileInfoRepository.getBySegment()
**Mục đích**: Lấy danh sách profiles thỏa mãn điều kiện (cho preview)

**Các bước xử lý:**
1. **Tạo SearchRequest**
   - Chỉ định multiple indices
   - Gắn BoolQueryBuilder
   - Thiết lập pagination (page, limit)
   - Thêm sorting (last_update DESC)

2. **Thực thi search query**
   - Gọi elasticsearchReadAdapter.searchAcrossMultipleIndices()
   - Lấy danh sách document IDs

3. **Lấy chi tiết profiles**
   - Gọi getPeopleFromTablesHBase() với danh sách IDs
   - Trả về List<ProfileInfo>

### 2.5 Preview Profiles

#### 2.5.1 API Endpoint
```
POST /audiences/preview-profiles
Header: user-id
Body: Audience object với segmentRule
```

#### 2.5.2 Các bước xử lý trong AudienceService.previewProfiles()
1. **Lấy Profile Management**
   - Gọi getProfileManagement() để lấy mapping configuration

2. **Xây dựng mapping maps**
   - Tạo mappingMap từ profileManagement
   - Tạo mappingMapIncludeSubField bao gồm sub-fields

3. **Xây dựng query**
   - Gọi QueryBuilderES.getSegmentQuery()
   - Kiểm tra có system data không

4. **Thực thi query và lấy profiles**
   - Gọi getFinalPeople() để lấy danh sách profiles
   - Áp dụng masking cho sensitive data

5. **Trả về kết quả**
   - AudienceResponse với danh sách profiles và metadata

### 2.6 Detail Profiles (Chi tiết profiles của audience)

#### 2.6.1 API Endpoint
```
GET /audiences/{id}/detail-profiles?page=0&limit=20
Header: user-id
```

#### 2.6.2 Các bước xử lý trong AudienceService.detailProfiles()
1. **Validate audience**
   - Lấy audience từ database bằng ID
   - Kiểm tra tồn tại → nếu không có throw AudienceNotFoundException

2. **Lấy configuration**
   - Convert AudienceEntity → Audience model
   - Lấy ProfileManagement của audience owner

3. **Xây dựng và thực thi query**
   - Tương tự như preview profiles
   - Nhưng có pagination và export support

4. **Trả về kết quả**
   - ProfileDetailDto với schema và profiles data

### 2.7 Get All Audiences (Lấy danh sách audiences)

#### 2.7.1 API Endpoint
```
GET /audiences?page=0&limit=20&search=&sort=&fromDate=&toDate=&dateField=&type=
Header: user-id
```

#### 2.7.2 Các bước xử lý trong AudienceService.getAll()
1. **Thiết lập pagination**
   - Tạo Pageable object với page và limit
   - Giới hạn limit tối đa 100

2. **Xử lý search và filter**
   - Nếu có search term → gọi searchAudiences()
   - Nếu có date range → filter theo created/updated date
   - Nếu không có filter → lấy tất cả audiences của user

3. **Thực thi query**
   - Gọi các method tương ứng trong audienceDao
   - Áp dụng sorting (mặc định DESC theo ID)

4. **Chuyển đổi và trả về**
   - Convert AudienceEntity → Audience model
   - Đếm tổng số records
   - Trả về AudienceResponse với data và metadata

### 2.8 Update Audience

#### 2.8.1 API Endpoint
```
PUT /audiences/{id}
Header: user-id
Body: Audience object với thông tin cập nhật
```

#### 2.8.2 Các bước xử lý trong AudienceService.updateAudience()
1. **Validate audience tồn tại**
   - Lấy audience từ database bằng ID
   - Kiểm tra tồn tại → nếu không có throw AudienceNotFoundException

2. **Cập nhật thông tin**
   - Convert entity → model
   - Gọi currentAudienceDto.update() để merge thông tin mới
   - Chỉ cập nhật các field không null

3. **Lưu vào database**
   - Gọi audienceDao.save() để lưu thay đổi

### 2.9 Delete Audience (Soft delete)

#### 2.9.1 API Endpoint
```
DELETE /audiences/{id}
Header: user-id
```

#### 2.9.2 Các bước xử lý trong AudienceService.deleteAudience()
1. **Validate audience tồn tại**
   - Lấy audience từ database bằng ID
   - Kiểm tra tồn tại → nếu không có throw AudienceNotFoundException

2. **Soft delete**
   - Set isDeleted = 1
   - Gọi audienceDao.save() để lưu thay đổi

### 2.10 Check Name Exists

#### 2.10.1 API Endpoint
```
GET /audiences/check-name?name=audience_name
Header: user-id
```

#### 2.10.2 Các bước xử lý trong AudienceService.checkNameExists()
1. **Kiểm tra tên trùng lặp**
   - Gọi audienceDao.getAudienceByNameAndUserIdAndIsDeleted()
   - Trả về true nếu tìm thấy audience với tên tương tự

### 2.11 Get Total Profiles của Multiple Audiences

#### 2.11.1 API Endpoint
```
POST /audiences/total-profiles
Header: user-id
Body: List<Long> audienceIds
```

#### 2.11.2 Các bước xử lý trong AudienceService.getTotalProfilesOfAudience()
1. **Lấy danh sách audiences**
   - Gọi audienceDao.getAllByIdIn() với list IDs

2. **Estimate cho từng audience**
   - Với mỗi audience, gọi estimateProfiles() với timeout
   - Sử dụng CompletableFuture để tránh timeout

3. **Xử lý timeout**
   - Nếu estimate thành công → cập nhật totalProfiles
   - Nếu timeout hoặc lỗi → lấy từ database (profileCount)

4. **Trả về Map**
   - Map<audienceId, totalProfiles>

## 3. Xử lý Lỗi và Exception

### 3.1 Các loại Exception
- **AudienceNotFoundException**: Không tìm thấy audience
- **ProfileNotFoundException**: Không tìm thấy profile management
- **InvalidParamException**: Tham số đầu vào không hợp lệ
- **RuntimeException**: Lỗi khi thực thi query Elasticsearch

### 3.2 Exception Handlers
- **AudienceExceptionHandler**: Xử lý audience-related exceptions
- **ProfileExceptionHandler**: Xử lý profile-related exceptions

## 4. Tối ưu hóa và Performance

### 4.1 Timeout Protection
- Sử dụng CompletableFuture cho estimate operations
- Timeout 2 giây để tránh treo hệ thống

### 4.2 Caching và Indexing
- Elasticsearch indexing cho fast search
- MySQL indexing trên userId, name, isDeleted

### 4.3 Pagination
- Giới hạn limit tối đa 100 records
- Sử dụng Spring Data Pageable

## 5. Security và Data Masking

### 5.1 Data Masking
- Kiểm tra maskingType trong mapping
- Áp dụng masking cho sensitive data
- System data được xử lý đặc biệt

### 5.2 User Isolation
- Mỗi user chỉ truy cập được audiences của mình
- User ID được hash bằng CRC32

## 6. Kết luận

Hệ thống xử lý audience được thiết kế với kiến trúc layered rõ ràng, tách biệt concerns và có khả năng mở rộng cao. Việc sử dụng Elasticsearch cho search và MySQL cho metadata storage đảm bảo performance tốt cho cả read và write operations.
